"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/context/AdminContext.tsx":
/*!**************************************!*\
  !*** ./src/context/AdminContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminContext: () => (/* binding */ AdminContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AdminContext,default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst AdminContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst AdminContextProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [aToken, setAToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [consultants, setConsultants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dashData, setDashData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const backendUrl = \"http://localhost:4000\" || 0;\n    // Initialize token from localStorage on client side\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminContextProvider.useEffect\": ()=>{\n            if (true) {\n                const token = localStorage.getItem('aToken');\n                if (token) {\n                    setAToken(token);\n                }\n            }\n        }\n    }[\"AdminContextProvider.useEffect\"], []);\n    const getAllConsultants = async ()=>{\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(backendUrl + '/api/admin/all-consultants', {\n                headers: {\n                    aToken\n                }\n            });\n            if (data.success) {\n                setConsultants(data.consultants);\n                console.log(data.consultants);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(data.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message);\n        }\n    };\n    const cancelAppointment = async (appointmentId)=>{\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(backendUrl + '/api/admin/cancel-appointment', {\n                appointmentId\n            }, {\n                headers: {\n                    aToken\n                }\n            });\n            if (data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(data.message);\n                getAllAppointments();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(data.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message);\n        }\n    };\n    const changeAvailability = async (docId)=>{\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(backendUrl + '/api/admin/change-availability', {\n                docId\n            }, {\n                headers: {\n                    aToken\n                }\n            });\n            if (data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(data.message);\n                getAllConsultants();\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(data.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message);\n        }\n    };\n    const getAllAppointments = async ()=>{\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(backendUrl + '/api/admin/appointments', {\n                headers: {\n                    aToken\n                }\n            });\n            if (data.success) {\n                setAppointments(data.appointments);\n                console.log(data.appointments);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(data.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message);\n        }\n    };\n    const getDashData = async ()=>{\n        try {\n            const { data } = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(backendUrl + '/api/admin/dashboard', {\n                headers: {\n                    aToken\n                }\n            });\n            if (data.success) {\n                setDashData(data.dashData);\n                console.log(data.dashData);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(data.message);\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(error.message);\n        }\n    };\n    const value = {\n        aToken,\n        setAToken,\n        backendUrl,\n        consultants,\n        getAllConsultants,\n        changeAvailability,\n        appointments,\n        setAppointments,\n        getAllAppointments,\n        cancelAppointment,\n        dashData,\n        getDashData\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\NEXT\\\\admin\\\\src\\\\context\\\\AdminContext.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AdminContextProvider, \"X/IXSabI1TzQqWTiPgA5MJ4yXgI=\");\n_c = AdminContextProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AdminContextProvider);\nvar _c;\n$RefreshReg$(_c, \"AdminContextProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/AdminContext.tsx\n"));

/***/ })

});