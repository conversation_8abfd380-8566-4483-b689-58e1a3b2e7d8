import React, { useState, useContext, FormEvent, ChangeEvent } from 'react';
import { assets } from '../../assets/assets';
import { AdminContext } from '../../context/AdminContext';
import { toast } from 'react-toastify';
import axios, { AxiosError } from 'axios';

// Type definitions for form data
type ExperienceOption =
  | '1 Year'
  | '2 Years'
  | '3 Years'
  | '4 Years'
  | '5 Years'
  | '6 Years'
  | '7 Years'
  | '8 Years'
  | '9 Years'
  | '10+ Years';
type SpecialityOption =
  | 'Engineer'
  | 'Legal'
  | 'Medic'
  | 'Construction'
  | 'Vehicles';

interface ApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

const AddConsultant: React.FC = () => {
  const [profImg, setProfImg] = useState<File | null>(null);
  const [name, setName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [experience, setExperience] = useState<ExperienceOption>('1 Year');
  const [fees, setFees] = useState<string>('');
  const [about, setAbout] = useState<string>('');
  const [speciality, setSpeciality] = useState<SpecialityOption>('Engineer');
  const [degree, setDegree] = useState<string>('');
  const [address1, setAddress1] = useState<string>('');
  const [address2, setAddress2] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const adminContext = useContext(AdminContext);

  if (!adminContext) {
    return <div>Loading...</div>;
  }

  const { backendUrl, aToken, getAllConsultants } = adminContext;

  const onSubmitHandler = async (
    event: FormEvent<HTMLFormElement>
  ): Promise<void> => {
    event.preventDefault();

    if (isSubmitting) {
      console.log('Form is already being submitted, ignoring...');
      return;
    }

    setIsSubmitting(true);
    console.log('=== FORM SUBMISSION STARTED ===');

    try {
      // Basic validation
      if (!profImg) {
        toast.error('Please select a profile image');
        return;
      }

      if (!name.trim()) {
        toast.error('Please enter consultant name');
        return;
      }

      if (!email.trim()) {
        toast.error('Please enter consultant email');
        return;
      }

      if (!password.trim()) {
        toast.error('Please enter password');
        return;
      }

      if (!about.trim()) {
        toast.error('Please enter about information');
        return;
      }

      // Note: fees, degree, and address are not required by the backend
      // but we'll keep them in the form for future use

      // Check backend configuration
      if (!backendUrl) {
        toast.error('Backend URL not configured');
        return;
      }

      if (!aToken) {
        toast.error('Admin token missing. Please login again.');
        return;
      }

      // Create FormData - match backend expectations
      const formData = new FormData();
      formData.append('image', profImg);
      formData.append('name', name.trim());
      formData.append('email', email.trim());
      formData.append('password', password.trim());
      formData.append('experience', experience);
      formData.append('about', about.trim());
      formData.append('specialization', speciality); // Backend expects 'specialization' not 'speciality'
      // Note: fees, degree, and address are not expected by the current backend

      console.log('=== FORM SUBMISSION DEBUG ===');
      console.log('Backend URL:', backendUrl);
      console.log('Admin Token:', aToken ? 'Present' : 'Missing');
      console.log('API Endpoint:', `${backendUrl}/api/admin/add-consultant`);
      console.log('Form data prepared, making API call...');

      // Log form data contents
      console.log('FormData contents (matching backend expectations):');
      for (let [key, value] of formData.entries()) {
        if (key === 'image') {
          console.log(
            `${key}: File - ${(value as File).name}, Size: ${(value as File).size} bytes`
          );
        } else {
          console.log(`${key}: ${value}`);
        }
      }

      console.log(
        'Backend expects: name, email, password, specialization, experience, about, image'
      );

      // Make API call
      console.log('Making API request...');
      const response = await axios.post<ApiResponse>(
        `${backendUrl}/api/admin/add-consultant`,
        formData,
        {
          headers: {
            aToken: aToken,
            // Don't set Content-Type manually for FormData, let axios handle it
          },
        }
      );
      console.log('API request completed successfully');

      console.log('API Response:', response.data);

      if (response.data.success) {
        toast.success(
          response.data.message || 'Consultant added successfully!'
        );

        // Clear form after successful submission
        setProfImg(null);
        setName('');
        setEmail('');
        setPassword('');
        setExperience('1 Year');
        setFees('');
        setAbout('');
        setSpeciality('Engineer');
        setDegree('');
        setAddress1('');
        setAddress2('');

        // Reset file input
        const fileInput = document.getElementById(
          'prof-img'
        ) as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }

        // Refresh consultants list
        try {
          await getAllConsultants();
        } catch (error) {
          console.error('Error refreshing consultants list:', error);
        }
      } else {
        toast.error(
          response.data.error ||
            response.data.message ||
            'Failed to add consultant'
        );
      }
    } catch (error) {
      const axiosError = error as AxiosError<ApiResponse>;
      console.error('Form submission error:', error);

      if (axiosError.response) {
        // Server responded with error status
        const status = axiosError.response.status;
        const errorData = axiosError.response.data;
        const errorMessage =
          errorData?.error || errorData?.message || 'Server error occurred';

        console.error('Server error response:', axiosError.response.data);
        toast.error(`Server Error (${status}): ${errorMessage}`);
      } else if (axiosError.request) {
        // Request was made but no response received
        console.error('No response from server:', axiosError.request);
        toast.error(
          'No response from server. Please check if the backend is running and accessible.'
        );
      } else {
        // Something else happened
        console.error('Request setup error:', axiosError.message);
        toast.error(`Request Error: ${axiosError.message}`);
      }
    } finally {
      setIsSubmitting(false);
      console.log('=== FORM SUBMISSION ENDED ===');
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (file) {
      setProfImg(file);
    }
  };

  // Test function to verify API connection
  const testApiConnection = async () => {
    try {
      console.log('Testing API connection...');
      console.log('Backend URL:', backendUrl);
      console.log('Token:', aToken ? 'Present' : 'Missing');

      const response = await axios.get(
        `${backendUrl}/api/admin/all-consultants`,
        {
          headers: {
            aToken: aToken,
          },
        }
      );

      console.log('API Test Response:', response.data);
      toast.success('API connection successful!');
    } catch (error) {
      console.error('API Test Error:', error);
      toast.error('API connection failed!');
    }
  };

  return (
    <form onSubmit={onSubmitHandler} className="m-5 w-full">
      <p className="mb-3 text-lg font-medium">Add Consultant</p>

      <div className="bg-white px-8 py-8 border rounded w-full max-w-4xl max-h-[80vh] overflow-y-scroll">
        <div className="flex items-center gap-4 mb-8 text-gray-500">
          <label htmlFor="prof-img">
            <img
              className="w-16 bg-gray-100 rounded-full cursor-pointer"
              src={profImg ? URL.createObjectURL(profImg) : assets.upload_area}
              alt=""
            />
          </label>
          <input onChange={handleFileChange} type="file" id="prof-img" hidden />
          <p>
            Upload <br /> picture
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-start gap-10 text-gray-600">
          <div className="w-full lg:flex-1 flex flex-col gap-4">
            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant name</p>
              <input
                onChange={e => setName(e.target.value)}
                value={name}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Name"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant Email</p>
              <input
                onChange={e => setEmail(e.target.value)}
                value={email}
                className="border rounded px-3 py-2"
                type="email"
                placeholder="Email"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant Password</p>
              <input
                onChange={e => setPassword(e.target.value)}
                value={password}
                className="border rounded px-3 py-2"
                type="password"
                placeholder="Password"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Experience</p>
              <select
                onChange={e =>
                  setExperience(e.target.value as ExperienceOption)
                }
                value={experience}
                className="border rounded px-3 py-2"
                aria-label="Experience"
              >
                <option value="1 Year">1 Year</option>
                <option value="2 Years">2 Years</option>
                <option value="3 Years">3 Years</option>
                <option value="4 Years">4 Years</option>
                <option value="5 Years">5 Years</option>
                <option value="6 Years">6 Years</option>
                <option value="7 Years">7 Years</option>
                <option value="8 Years">8 Years</option>
                <option value="9 Years">9 Years</option>
                <option value="10+ Years">10+ Years</option>
              </select>
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Fees</p>
              <input
                onChange={e => setFees(e.target.value)}
                value={fees}
                className="border rounded px-3 py-2"
                type="number"
                placeholder="Fees"
                required
              />
            </div>
          </div>

          <div className="w-full lg:flex-1 flex flex-col gap-4">
            <div className="flex-1 flex flex-col gap-1">
              <p>Speciality</p>
              <select
                onChange={e =>
                  setSpeciality(e.target.value as SpecialityOption)
                }
                value={speciality}
                className="border rounded px-3 py-2"
                aria-label="Speciality"
              >
                <option value="Engineer">Engineer</option>
                <option value="Legal">Lawyer</option>
                <option value="Medic">Medic</option>
                <option value="Construction">Construction</option>
                <option value="Vehicles">Driving Instructor</option>
              </select>
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Education</p>
              <input
                onChange={e => setDegree(e.target.value)}
                value={degree}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Education"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Address</p>
              <input
                onChange={e => setAddress1(e.target.value)}
                value={address1}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Address 1"
                required
              />
              <input
                onChange={e => setAddress2(e.target.value)}
                value={address2}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Address 2"
                required
              />
            </div>
          </div>
        </div>

        <div className="mt-4 text-gray-600">
          <p>About Consultant</p>
          <textarea
            onChange={e => setAbout(e.target.value)}
            value={about}
            className="w-full px-4 pt-2 border rounded"
            placeholder="Write about consultant"
            rows={5}
            required
          />
        </div>

        <div className="flex gap-4 mt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-10 py-3 text-white rounded-full border-2 ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-primary hover:bg-primary/90'
            }`}
          >
            {isSubmitting ? 'Adding Consultant...' : 'Add consultant'}
          </button>
          <button
            type="button"
            onClick={testApiConnection}
            className="bg-gray-500 px-6 py-3 text-white rounded-full border-2 hover:bg-gray-600"
          >
            Test API
          </button>
        </div>
      </div>
    </form>
  );
};

export default AddConsultant;
