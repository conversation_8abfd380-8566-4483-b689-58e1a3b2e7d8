import React, { useState, useContext, FormEvent, ChangeEvent } from 'react';
import { assets } from '../../assets/assets';
import { AdminContext } from '../../context/AdminContext';
import { toast } from 'react-toastify';
import axios, { AxiosError } from 'axios';

// Type definitions for form data
type ExperienceOption =
  | '1 Year'
  | '2 Years'
  | '3 Years'
  | '4 Years'
  | '5 Years'
  | '6 Years'
  | '7 Years'
  | '8 Years'
  | '9 Years'
  | '10+ Years';
type SpecialityOption =
  | 'Engineer'
  | 'Legal'
  | 'Medic'
  | 'Construction'
  | 'Vehicles';

interface ApiResponse {
  success: boolean;
  message?: string;
  error?: string;
}

const AddConsultant: React.FC = () => {
  const [profImg, setProfImg] = useState<File | null>(null);
  const [name, setName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [experience, setExperience] = useState<ExperienceOption>('1 Year');
  const [fees, setFees] = useState<string>('');
  const [about, setAbout] = useState<string>('');
  const [speciality, setSpeciality] = useState<SpecialityOption>('Engineer');
  const [degree, setDegree] = useState<string>('');
  const [address1, setAddress1] = useState<string>('');
  const [address2, setAddress2] = useState<string>('');

  const adminContext = useContext(AdminContext);

  if (!adminContext) {
    return <div>Loading...</div>;
  }

  const { backendUrl, aToken } = adminContext;

  const onSubmitHandler = async (
    event: FormEvent<HTMLFormElement>
  ): Promise<void> => {
    event.preventDefault();

    // Add validation to check all fields before sending
    const requiredFields = {
      image: profImg,
      name: name.trim(),
      email: email.trim(),
      password: password.trim(),
      experience,
      fees: fees.trim(),
      about: about.trim(),
      speciality,
      degree: degree.trim(),
      address1: address1.trim(),
      address2: address2.trim(),
    };

    // Check for empty fields
    const emptyFields = Object.entries(requiredFields).filter(
      ([key, value]) => {
        if (key === 'image') return !value;
        return !value || value === '';
      }
    );

    if (emptyFields.length > 0) {
      console.log('Empty fields:', emptyFields);
      return toast.error(
        `Please fill all required fields: ${emptyFields.map(([key]) => key).join(', ')}`
      );
    }

    console.log('All fields validated, proceeding with submission...');

    try {
      if (!profImg) {
        return toast.error('Image Not Selected');
      }

      // Debug logging
      console.log('Backend URL:', backendUrl);
      console.log('Token:', aToken);

      if (!backendUrl) {
        return toast.error('Backend URL not configured');
      }

      if (!aToken) {
        return toast.error('Admin token missing');
      }

      const formData = new FormData();
      formData.append('image', profImg);
      formData.append('name', name);
      formData.append('email', email);
      formData.append('password', password);
      formData.append('experience', experience);
      formData.append('fees', fees);
      formData.append('about', about);
      formData.append('speciality', speciality);
      formData.append('degree', degree);
      formData.append(
        'address',
        JSON.stringify({ line1: address1, line2: address2 })
      );

      // console log formData
      formData.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });

      const { data } = await axios.post<ApiResponse>(
        backendUrl + '/api/admin/add-consultant',
        formData,
        {
          headers: {
            aToken,
          },
        }
      );

      console.log('Response data:', data);

      if (data.success) {
        toast.success(data.message);
        setProfImg(null);
        setName('');
        setPassword('');
        setEmail('');
        setAddress1('');
        setAddress2('');
        setDegree('');
        setAbout('');
        setFees('');
      } else {
        toast.error(data.error || data.message || 'Unknown error');
      }
    } catch (error) {
      const axiosError = error as AxiosError<ApiResponse>;
      console.log('Full error:', error);
      console.log('Response:', axiosError.response);
      console.log('Backend URL:', backendUrl);

      if (axiosError.response) {
        const errorMessage =
          axiosError.response.data?.error ||
          axiosError.response.data?.message ||
          'Unknown error';
        toast.error(
          `Server error: ${axiosError.response.status} - ${errorMessage}`
        );
      } else if (axiosError.request) {
        toast.error('No response from server. Check if backend is running.');
      } else {
        toast.error(`Request error: ${axiosError.message}`);
      }
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (file) {
      setProfImg(file);
    }
  };

  return (
    <form onSubmit={onSubmitHandler} className="m-5 w-full">
      <p className="mb-3 text-lg font-medium">Add Consultant</p>

      <div className="bg-white px-8 py-8 border rounded w-full max-w-4xl max-h-[80vh] overflow-y-scroll">
        <div className="flex items-center gap-4 mb-8 text-gray-500">
          <label htmlFor="prof-img">
            <img
              className="w-16 bg-gray-100 rounded-full cursor-pointer"
              src={profImg ? URL.createObjectURL(profImg) : assets.upload_area}
              alt=""
            />
          </label>
          <input onChange={handleFileChange} type="file" id="prof-img" hidden />
          <p>
            Upload <br /> picture
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-start gap-10 text-gray-600">
          <div className="w-full lg:flex-1 flex flex-col gap-4">
            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant name</p>
              <input
                onChange={e => setName(e.target.value)}
                value={name}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Name"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant Email</p>
              <input
                onChange={e => setEmail(e.target.value)}
                value={email}
                className="border rounded px-3 py-2"
                type="email"
                placeholder="Email"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Consultant Password</p>
              <input
                onChange={e => setPassword(e.target.value)}
                value={password}
                className="border rounded px-3 py-2"
                type="password"
                placeholder="Password"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Experience</p>
              <select
                onChange={e =>
                  setExperience(e.target.value as ExperienceOption)
                }
                value={experience}
                className="border rounded px-3 py-2"
                aria-label="Experience"
              >
                <option value="1 Year">1 Year</option>
                <option value="2 Years">2 Years</option>
                <option value="3 Years">3 Years</option>
                <option value="4 Years">4 Years</option>
                <option value="5 Years">5 Years</option>
                <option value="6 Years">6 Years</option>
                <option value="7 Years">7 Years</option>
                <option value="8 Years">8 Years</option>
                <option value="9 Years">9 Years</option>
                <option value="10+ Years">10+ Years</option>
              </select>
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Fees</p>
              <input
                onChange={e => setFees(e.target.value)}
                value={fees}
                className="border rounded px-3 py-2"
                type="number"
                placeholder="Fees"
                required
              />
            </div>
          </div>

          <div className="w-full lg:flex-1 flex flex-col gap-4">
            <div className="flex-1 flex flex-col gap-1">
              <p>Speciality</p>
              <select
                onChange={e =>
                  setSpeciality(e.target.value as SpecialityOption)
                }
                value={speciality}
                className="border rounded px-3 py-2"
                aria-label="Speciality"
              >
                <option value="Engineer">Engineer</option>
                <option value="Legal">Lawyer</option>
                <option value="Medic">Medic</option>
                <option value="Construction">Construction</option>
                <option value="Vehicles">Driving Instructor</option>
              </select>
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Education</p>
              <input
                onChange={e => setDegree(e.target.value)}
                value={degree}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Education"
                required
              />
            </div>

            <div className="flex-1 flex flex-col gap-1">
              <p>Address</p>
              <input
                onChange={e => setAddress1(e.target.value)}
                value={address1}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Address 1"
                required
              />
              <input
                onChange={e => setAddress2(e.target.value)}
                value={address2}
                className="border rounded px-3 py-2"
                type="text"
                placeholder="Address 2"
                required
              />
            </div>
          </div>
        </div>

        <div className="mt-4 text-gray-600">
          <p>About Consultant</p>
          <textarea
            onChange={e => setAbout(e.target.value)}
            value={about}
            className="w-full px-4 pt-2 border rounded"
            placeholder="Write about consultant"
            rows={5}
            required
          />
        </div>

        <button
          type="submit"
          className="bg-primary px-10 py-3 mt-4 text-white rounded-full border-2 "
        >
          Add consultant
        </button>
      </div>
    </form>
  );
};

export default AddConsultant;
