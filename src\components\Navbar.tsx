'use client';

import React, { useContext } from 'react';
import { assets } from '@/assets/assets';
import { AdminContext } from '@/context/AdminContext';
import { useRouter } from 'next/navigation';
import { ConsultantContext } from '@/context/ConsultantContext';

const Navbar: React.FC = () => {
  const adminContext = useContext(AdminContext);
  const consultantContext = useContext(ConsultantContext);
  const router = useRouter();

  if (!adminContext || !consultantContext) {
    return null;
  }

  const { aToken, setAToken } = adminContext;
  const { dToken, setDToken } = consultantContext;

  const logout = () => {
    router.push('/');
    if (aToken) {
      setAToken('');
      localStorage.removeItem('aToken');
    }
    if (dToken) {
      setDToken('');
      localStorage.removeItem('dToken');
    }
  };

  return (
    <div className="flex justify-between items-center px-4 sm:px-10 py-3 border-b bg-white">
      <div className="flex items-center gap-2 text-xs">
        <img
          className="w-36 sm:w-40 cursor-pointer"
          src={assets.admin_logo}
          alt="Admin Logo"
        />
        <p className="border px-2.5 py-0.5 rounded-full border-gray-500 text-gray-600">
          {aToken ? 'Admin' : 'Consultant'}
        </p>
      </div>
      <button
        onClick={logout}
        className="bg-primary text-white text-sm px-10 py-2 rounded-full border-2"
      >
        Logout
      </button>
    </div>
  );
};

export default Navbar;
